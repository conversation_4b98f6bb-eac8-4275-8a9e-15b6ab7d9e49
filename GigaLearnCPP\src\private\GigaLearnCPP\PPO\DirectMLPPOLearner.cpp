#include "GigaLearnCPP/PPO/DirectMLPPOLearner.h"
#include "PPOLearner.h"
#include "ExperienceBuffer.h"
#include <GigaLearnCPP/PPO/TransferLearnConfig.h>
#include <chrono>

namespace GGL {

DirectMLPPOLearner::DirectMLPPOLearner(int obsSize, int numActions, const Config& cfg, torch::Device device)
    : config(cfg) {
    
    RG_LOG("Initializing DirectML PPO Learner...");
    
    // Always initialize CPU learner as fallback
    if (!InitializeCPULearner(obsSize, numActions, device)) {
        RG_ERR_CLOSE("Failed to initialize CPU learner");
    }
    
    // Try to initialize DirectML if enabled
    if (config.enableDirectML) {
        if (InitializeDirectML(obsSize, numActions)) {
            usingDirectML = true;
            RG_LOG(" > DirectML acceleration enabled");
        } else {
            if (config.fallbackToCPU) {
                usingDirectML = false;
                RG_LOG(" > DirectML failed, using CPU fallback");
            } else {
                RG_ERR_CLOSE("DirectML initialization failed and fallback disabled");
            }
        }
    } else {
        usingDirectML = false;
        RG_LOG(" > DirectML disabled, using CPU only");
    }
}

DirectMLPPOLearner::~DirectMLPPOLearner() {
    delete cpuLearner;
}

bool DirectMLPPOLearner::InitializeDirectML(int obsSize, int numActions) {
    try {
        // Create DirectML service
        directMLService = CreateDirectMLService(
            obsSize,
            numActions,
            config.ppo.policy.layerSizes,
            config.ppo.critic.layerSizes,
            config.ppo.sharedHead.layerSizes
        );
        
        if (!directMLService || !directMLService->IsAvailable()) {
            RG_LOG("DirectML service initialization failed");
            return false;
        }
        
        RG_LOG(" > DirectML service initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        RG_LOG("DirectML initialization exception: " << e.what());
        return false;
    }
}

bool DirectMLPPOLearner::InitializeCPULearner(int obsSize, int numActions, torch::Device device) {
    try {
        cpuLearner = new PPOLearner(obsSize, numActions, config.ppo, device);
        RG_LOG(" > CPU learner initialized");
        return true;
    } catch (const std::exception& e) {
        RG_LOG("CPU learner initialization failed: " << e.what());
        return false;
    }
}

void DirectMLPPOLearner::InferActions(
    torch::Tensor obs, 
    torch::Tensor actionMasks, 
    torch::Tensor* outActions, 
    torch::Tensor* outLogProbs) {
    
    inferenceCount++;
    
    // Use DirectML for inference if available and beneficial
    if (usingDirectML && ShouldUseDirectML()) {
        try {
            auto startTime = std::chrono::high_resolution_clock::now();
            
            // Convert tensors to vectors for DirectML service
            auto obsVec = TensorToVector(obs);
            auto maskVec = std::vector<uint8_t>();
            
            if (actionMasks.defined()) {
                auto maskData = actionMasks.contiguous().data_ptr<uint8_t>();
                maskVec.assign(maskData, maskData + actionMasks.numel());
            }
            
            // Perform DirectML inference
            auto result = directMLService->ForwardPass(obsVec, maskVec, "policy");
            
            if (result.success) {
                // Convert results back to tensors
                auto batchSize = obs.size(0);
                auto numActions = result.output.size() / batchSize;
                
                if (outActions) {
                    // Sample actions from probabilities
                    auto probsTensor = VectorToTensor(result.probabilities, {batchSize, (int64_t)numActions});
                    *outActions = torch::multinomial(probsTensor, 1).squeeze(-1);
                }
                
                if (outLogProbs) {
                    auto logProbsTensor = VectorToTensor(result.logProbabilities, {batchSize, (int64_t)numActions});
                    if (outActions) {
                        *outLogProbs = logProbsTensor.gather(1, outActions->unsqueeze(-1)).squeeze(-1);
                    } else {
                        *outLogProbs = logProbsTensor;
                    }
                }
                
                auto endTime = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration<float, std::milli>(endTime - startTime).count();
                
                // Update performance metrics
                static float totalDirectMLTime = 0.0f;
                static int directMLCount = 0;
                totalDirectMLTime += duration;
                directMLCount++;
                
                return; // Success with DirectML
            }
        } catch (const std::exception& e) {
            RG_LOG("DirectML inference failed, falling back to CPU: " << e.what());
        }
    }
    
    // Fallback to CPU inference
    cpuLearner->InferActions(obs, actionMasks, outActions, outLogProbs);
}

torch::Tensor DirectMLPPOLearner::InferCritic(torch::Tensor obs) {
    // For now, always use CPU for critic inference
    // DirectML critic inference can be added later
    return cpuLearner->InferCritic(obs);
}

void DirectMLPPOLearner::Learn(ExperienceBuffer& experience, Report& report, bool isFirstIteration) {
    // Training always uses CPU learner for now
    // This ensures compatibility and stability
    cpuLearner->Learn(experience, report, isFirstIteration);
    
    // Log DirectML performance metrics
    LogPerformanceMetrics(report);
}

void DirectMLPPOLearner::TransferLearn(
    ModelSet& oldModels,
    torch::Tensor newObs, torch::Tensor oldObs,
    torch::Tensor newActionMasks, torch::Tensor oldActionMasks,
    torch::Tensor actionMaps,
    Report& report,
    const TransferLearnConfig& tlConfig) {
    
    // Transfer learning uses CPU learner
    cpuLearner->TransferLearn(oldModels, newObs, oldObs, newActionMasks, oldActionMasks, actionMaps, report, tlConfig);
}

void DirectMLPPOLearner::Save(std::filesystem::path folder, bool saveOptims) {
    cpuLearner->models.Save(folder, saveOptims);
}

void DirectMLPPOLearner::Load(std::filesystem::path folder, bool allowNotExist, bool loadOptims) {
    cpuLearner->models.Load(folder, allowNotExist, loadOptims);
}

ModelSet& DirectMLPPOLearner::GetModels() {
    return cpuLearner->models;
}

void DirectMLPPOLearner::LogPerformanceMetrics(Report& report) {
    if (directMLService) {
        directMLService->LogPerformanceMetrics(report);
    }
    
    report["DirectML Enabled"] = usingDirectML ? 1.0f : 0.0f;
    report["DirectML Inference Count"] = (float)inferenceCount;
    
    if (usingDirectML) {
        report["DirectML Speedup"] = directMLSpeedup;
    }
}

std::map<std::string, std::string> DirectMLPPOLearner::GetDeviceInfo() {
    if (directMLService) {
        return directMLService->GetDeviceInfo();
    } else {
        return {{"status", "directml_not_available"}};
    }
}

bool DirectMLPPOLearner::ShouldUseDirectML() const {
    // Simple heuristic: use DirectML if it's available and we're not in training mode
    return usingDirectML && directMLService && directMLService->IsAvailable();
}

std::vector<float> DirectMLPPOLearner::TensorToVector(const torch::Tensor& tensor) {
    auto flatTensor = tensor.contiguous().cpu();
    auto dataPtr = flatTensor.data_ptr<float>();
    return std::vector<float>(dataPtr, dataPtr + flatTensor.numel());
}

torch::Tensor DirectMLPPOLearner::VectorToTensor(const std::vector<float>& vec, const std::vector<int64_t>& shape) {
    auto tensor = torch::from_blob(const_cast<float*>(vec.data()), shape, torch::kFloat);
    return tensor.clone();
}

// Factory function
std::unique_ptr<DirectMLPPOLearner> CreateDirectMLPPOLearner(
    int obsSize,
    int numActions,
    const PPOLearnerConfig& ppoConfig,
    torch::Device device,
    bool enableDirectML) {
    
    DirectMLPPOLearner::Config config;
    config.ppo = ppoConfig;
    config.enableDirectML = enableDirectML;
    config.fallbackToCPU = true;
    config.hybridMode = true;
    
    auto learner = std::make_unique<DirectMLPPOLearner>(obsSize, numActions, config, device);
    
    return learner;
}

} // namespace GGL

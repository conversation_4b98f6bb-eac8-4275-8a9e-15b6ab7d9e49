"""
DirectML Service for GigaLearnCPP
Provides GPU acceleration for AMD GPUs using DirectML backend
"""

import sys
import os
import traceback
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import json

# Global variables
directml_available = False
torch_available = False
device = None
models = {}
config = {}

def check_dependencies():
    """Check if required dependencies are available"""
    global directml_available, torch_available
    
    try:
        import torch
        torch_available = True
        print("✓ PyTorch available")
    except ImportError:
        print("✗ PyTorch not available")
        return False
    
    try:
        import torch_directml
        directml_available = True
        print("✓ DirectML available")
        return True
    except ImportError:
        print("✗ DirectML not available - falling back to CPU")
        return False

def init_directml():
    """Initialize DirectML device"""
    global device, directml_available

    if not check_dependencies():
        return False

    try:
        import torch

        if directml_available:
            import torch_directml
            device = torch_directml.device()
            print(f"✓ DirectML device initialized: {device}")

            # Test basic operations
            test_tensor = torch.randn(100, 100).to(device)
            result = torch.mm(test_tensor, test_tensor.t())
            print("✓ DirectML basic operations working")
            return True
        else:
            device = torch.device('cpu')
            print("✓ Fallback to CPU device")
            return True

    except Exception as e:
        print(f"✗ DirectML initialization failed: {e}")
        traceback.print_exc()
        return False

def create_model(model_name: str, layer_sizes: List[int], input_size: int, output_size: int, 
                activation: str = "relu", add_layer_norm: bool = True, add_output_layer: bool = True):
    """Create a neural network model matching GigaLearnCPP architecture"""
    
    if not torch_available:
        raise RuntimeError("PyTorch not available")
    
    import torch
    import torch.nn as nn
    
    layers = []
    last_size = input_size
    
    # Hidden layers
    for size in layer_sizes:
        layers.append(nn.Linear(last_size, size))
        
        if add_layer_norm:
            layers.append(nn.LayerNorm(size))
        
        # Add activation
        if activation.lower() == "relu":
            layers.append(nn.ReLU())
        elif activation.lower() == "leaky_relu":
            layers.append(nn.LeakyReLU())
        elif activation.lower() == "sigmoid":
            layers.append(nn.Sigmoid())
        elif activation.lower() == "tanh":
            layers.append(nn.Tanh())
        
        last_size = size
    
    # Output layer
    if add_output_layer:
        layers.append(nn.Linear(last_size, output_size))
    
    model = nn.Sequential(*layers)
    
    # Move to device
    if device is not None:
        model = model.to(device)
    
    return model

def init_models(model_configs: Dict[str, Any]):
    """Initialize models from configuration"""
    global models, config
    
    config = model_configs
    models = {}
    
    try:
        # Create shared head if specified
        if "shared_head" in model_configs and model_configs["shared_head"]["layer_sizes"]:
            models["shared_head"] = create_model(
                "shared_head",
                model_configs["shared_head"]["layer_sizes"],
                model_configs["obs_size"],
                model_configs["shared_head"]["layer_sizes"][-1],  # Output size is last layer size
                model_configs["shared_head"].get("activation", "relu"),
                model_configs["shared_head"].get("add_layer_norm", True),
                False  # Shared head doesn't have output layer
            )
            shared_output_size = model_configs["shared_head"]["layer_sizes"][-1]
        else:
            shared_output_size = model_configs["obs_size"]
        
        # Create policy model
        models["policy"] = create_model(
            "policy",
            model_configs["policy"]["layer_sizes"],
            shared_output_size,
            model_configs["num_actions"],
            model_configs["policy"].get("activation", "relu"),
            model_configs["policy"].get("add_layer_norm", True),
            True
        )
        
        # Create critic model
        models["critic"] = create_model(
            "critic",
            model_configs["critic"]["layer_sizes"],
            shared_output_size,
            1,  # Critic outputs single value
            model_configs["critic"].get("activation", "relu"),
            model_configs["critic"].get("add_layer_norm", True),
            True
        )
        
        print(f"✓ Models initialized: {list(models.keys())}")
        return True
        
    except Exception as e:
        print(f"✗ Model initialization failed: {e}")
        traceback.print_exc()
        return False

def forward_pass(obs_data: np.ndarray, action_masks: np.ndarray, model_name: str = "policy") -> Dict[str, np.ndarray]:
    """Perform forward pass through specified model"""
    
    if not torch_available or model_name not in models:
        raise RuntimeError(f"Model {model_name} not available")
    
    try:
        import torch
        
        # Convert numpy arrays to tensors
        obs_tensor = torch.from_numpy(obs_data).float()
        mask_tensor = torch.from_numpy(action_masks).float() if action_masks is not None else None
        
        # Move to device
        if device is not None:
            obs_tensor = obs_tensor.to(device)
            if mask_tensor is not None:
                mask_tensor = mask_tensor.to(device)
        
        with torch.no_grad():
            # Forward through shared head if it exists
            if "shared_head" in models:
                shared_output = models["shared_head"](obs_tensor)
            else:
                shared_output = obs_tensor
            
            # Forward through specified model
            output = models[model_name](shared_output)
            
            # Convert back to numpy
            result = {
                "output": output.cpu().numpy()
            }
            
            # For policy, also compute action probabilities
            if model_name == "policy" and mask_tensor is not None:
                # Apply softmax to get probabilities
                logits = output
                if mask_tensor is not None:
                    # Mask invalid actions
                    logits = logits + (mask_tensor - 1) * 1e9
                
                probs = torch.softmax(logits, dim=-1)
                result["probabilities"] = probs.cpu().numpy()
                result["log_probabilities"] = torch.log_softmax(logits, dim=-1).cpu().numpy()
        
        return result
        
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        traceback.print_exc()
        raise

def get_device_info():
    """Get information about the current device"""
    global device, directml_available
    
    info = {
        "directml_available": directml_available,
        "torch_available": torch_available,
        "device": str(device) if device is not None else "None",
        "models_loaded": list(models.keys())
    }
    
    if torch_available:
        import torch
        info["torch_version"] = torch.__version__
        
        if directml_available:
            try:
                import torch_directml
                info["directml_version"] = torch_directml.__version__
            except:
                pass
    
    return info

# Initialize on import
if __name__ == "__main__" or True:  # Always initialize when imported
    init_directml()

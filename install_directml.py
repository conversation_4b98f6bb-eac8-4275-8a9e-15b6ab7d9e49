"""
DirectML Installation Script for GigaLearnCPP
Installs torch-directml and tests compatibility
"""

import subprocess
import sys
import os
import platform

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{description}...")
    print(f"Running: {cmd}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {description} successful")
            if result.stdout:
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ {description} failed")
            print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ {description} failed with exception: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 7:
        print("✓ Python version compatible")
        return True
    else:
        print("✗ Python version not compatible (requires Python 3.7+)")
        return False

def check_system_requirements():
    """Check system requirements"""
    print(f"Operating System: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    
    if platform.system() != "Windows":
        print("⚠ DirectML is primarily designed for Windows")
        return False
    
    return True

def install_pytorch():
    """Install PyTorch CPU version"""
    print("\nInstalling PyTorch...")
    
    # Install PyTorch CPU version (compatible with DirectML)
    cmd = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    return run_command(cmd, "PyTorch installation")

def install_directml():
    """Install torch-directml"""
    print("\nInstalling torch-directml...")
    
    # Install torch-directml
    cmd = f"{sys.executable} -m pip install torch-directml"
    return run_command(cmd, "torch-directml installation")

def test_installation():
    """Test the DirectML installation"""
    print("\nTesting DirectML installation...")
    
    test_script = """
import sys
try:
    import torch
    print(f"[OK] PyTorch {torch.__version__} imported successfully")

    import torch_directml
    print(f"[OK] torch-directml imported successfully")

    # Test device creation
    device = torch_directml.device()
    print(f"[OK] DirectML device created: {device}")

    # Test basic operations
    x = torch.randn(100, 100).to(device)
    y = torch.mm(x, x.t())
    print(f"[OK] Basic tensor operations working")

    # Test model creation
    model = torch.nn.Sequential(
        torch.nn.Linear(100, 50),
        torch.nn.ReLU(),
        torch.nn.Linear(50, 10)
    ).to(device)

    with torch.no_grad():
        output = model(x[:10])
    print(f"[OK] Model inference working, output shape: {output.shape}")

    print("\\n[SUCCESS] DirectML installation successful!")
    sys.exit(0)

except ImportError as e:
    print(f"[ERROR] Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"[ERROR] Test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
"""
    
    # Write test script to temporary file
    with open("test_directml.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    try:
        # Run test script
        result = subprocess.run([sys.executable, "test_directml.py"], 
                              capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print(f"Warnings/Errors: {result.stderr}")
        
        success = result.returncode == 0
        
        # Clean up
        if os.path.exists("test_directml.py"):
            os.remove("test_directml.py")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("✗ Test timed out")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_gigalearn_integration():
    """Test integration with GigaLearnCPP"""
    print("\nTesting GigaLearnCPP integration...")
    
    try:
        # Test importing our DirectML service
        sys.path.append("python_scripts")
        import directml_service
        
        info = directml_service.get_device_info()
        print("Device info:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        if info["directml_available"]:
            print("✓ DirectML service integration working")
            return True
        else:
            print("⚠ DirectML not available, but service can fallback to CPU")
            return True
            
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main installation process"""
    print("=== DirectML Installation for GigaLearnCPP ===")
    
    # Check requirements
    if not check_python_version():
        return False
    
    if not check_system_requirements():
        print("⚠ Proceeding anyway, but DirectML may not work optimally")
    
    # Install packages
    if not install_pytorch():
        print("Failed to install PyTorch")
        return False
    
    if not install_directml():
        print("Failed to install torch-directml")
        return False
    
    # Test installation
    if not test_installation():
        print("DirectML installation test failed")
        return False
    
    # Test integration
    if not test_gigalearn_integration():
        print("GigaLearnCPP integration test failed")
        return False
    
    print("\n🎉 All tests passed! DirectML is ready for use with GigaLearnCPP")
    print("\nNext steps:")
    print("1. Rebuild your GigaLearnCPP project")
    print("2. The system will automatically use DirectML if available")
    print("3. Monitor performance improvements in your training metrics")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

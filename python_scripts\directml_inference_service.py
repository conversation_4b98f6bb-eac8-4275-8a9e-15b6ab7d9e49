"""
Simple DirectML Inference Service for GigaLearnCPP
Provides GPU-accelerated inference for existing models
"""

import sys
import os
import traceback
import numpy as np
import time

# Global variables
directml_available = False
torch_available = False
device = None
models = {}

def init_directml():
    """Initialize DirectML device"""
    global device, directml_available, torch_available
    
    try:
        import torch
        torch_available = True
        print("✓ PyTorch available")
    except ImportError:
        print("✗ PyTorch not available")
        return False
    
    try:
        import torch_directml
        directml_available = True
        device = torch_directml.device()
        print(f"✓ DirectML device initialized: {device}")
        
        # Test basic operations
        test_tensor = torch.randn(100, 100).to(device)
        result = torch.mm(test_tensor, test_tensor.t())
        print("✓ DirectML basic operations working")
        return True
        
    except ImportError:
        print("✗ DirectML not available - falling back to CPU")
        device = torch.device('cpu')
        return True
    except Exception as e:
        print(f"✗ DirectML initialization failed: {e}")
        device = torch.device('cpu')
        return True

def load_model_from_checkpoint(model_path, model_type="policy"):
    """Load a model from GigaLearnCPP checkpoint format"""
    global models
    
    if not torch_available:
        return False
    
    try:
        import torch
        
        # Load the model
        model = torch.jit.load(model_path, map_location=device)
        model.eval()
        
        models[model_type] = model
        print(f"✓ Loaded {model_type} model from {model_path}")
        return True
        
    except Exception as e:
        print(f"✗ Failed to load model {model_path}: {e}")
        return False

def inference_batch(observations, action_masks=None, model_type="policy"):
    """Perform batch inference using DirectML"""
    
    if model_type not in models:
        raise RuntimeError(f"Model {model_type} not loaded")
    
    try:
        import torch
        
        # Convert inputs to tensors
        obs_tensor = torch.from_numpy(observations).float().to(device)
        
        if action_masks is not None:
            mask_tensor = torch.from_numpy(action_masks).bool().to(device)
        else:
            mask_tensor = None
        
        # Perform inference
        with torch.no_grad():
            output = models[model_type](obs_tensor)
            
            # Apply action masking if provided
            if mask_tensor is not None and model_type == "policy":
                # Mask invalid actions with very negative values
                output = output + (mask_tensor.logical_not().float() * -1e9)
            
            # Convert to probabilities for policy
            if model_type == "policy":
                probs = torch.softmax(output, dim=-1)
                log_probs = torch.log_softmax(output, dim=-1)
                
                return {
                    "logits": output.cpu().numpy(),
                    "probabilities": probs.cpu().numpy(),
                    "log_probabilities": log_probs.cpu().numpy()
                }
            else:
                return {
                    "output": output.cpu().numpy()
                }
                
    except Exception as e:
        print(f"✗ Inference failed: {e}")
        traceback.print_exc()
        raise

def sample_actions(probabilities):
    """Sample actions from probability distributions"""
    try:
        import torch
        
        probs_tensor = torch.from_numpy(probabilities).to(device)
        actions = torch.multinomial(probs_tensor, 1).squeeze(-1)
        
        return actions.cpu().numpy()
        
    except Exception as e:
        print(f"✗ Action sampling failed: {e}")
        raise

def get_device_info():
    """Get device information"""
    return {
        "directml_available": directml_available,
        "torch_available": torch_available,
        "device": str(device) if device is not None else "None",
        "models_loaded": list(models.keys())
    }

def benchmark_inference(batch_size=64, num_iterations=100):
    """Benchmark inference performance"""
    if "policy" not in models:
        print("No policy model loaded for benchmarking")
        return
    
    try:
        import torch
        
        # Create dummy data
        obs_size = 107  # Typical RL observation size
        num_actions = 8  # Typical action space
        
        observations = np.random.randn(batch_size, obs_size).astype(np.float32)
        action_masks = np.ones((batch_size, num_actions), dtype=np.bool_)
        
        # Warm up
        for _ in range(10):
            result = inference_batch(observations, action_masks, "policy")
        
        # Benchmark
        start_time = time.time()
        for _ in range(num_iterations):
            result = inference_batch(observations, action_masks, "policy")
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time = total_time / num_iterations * 1000  # ms
        
        print(f"DirectML inference: {avg_time:.2f} ms per batch (batch_size={batch_size})")
        return avg_time
        
    except Exception as e:
        print(f"Benchmark failed: {e}")
        return None

# Initialize on import
if __name__ == "__main__" or True:
    init_directml()

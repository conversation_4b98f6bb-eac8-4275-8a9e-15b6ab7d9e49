#pragma once

#include "PPOLearnerConfig.h"
#include "../Util/DirectMLService.h"
#include "../Util/Report.h"
#include <memory>

// Forward declarations
namespace torch {
    class Tensor;
    class Device;
}

namespace GGL {

    /**
     * <PERSON><PERSON> Learner with DirectML acceleration
     * Extends the standard PPO learner to use DirectML for GPU operations on AMD GPUs
     */
    class RG_IMEXPORT DirectMLPPOLearner {
    public:
        struct Config {
            PPOLearnerConfig ppo;
            bool enableDirectML = true;
            bool fallbackToCPU = true;
            bool hybridMode = true;  // Use DirectML for inference, CPU for training
        };

    private:
        Config config;
        std::unique_ptr<DirectMLService> directMLService;
        
        // Standard CPU models for fallback/training
        class PPOLearner* cpuLearner = nullptr;
        
        // Performance tracking
        bool usingDirectML = false;
        float directMLSpeedup = 1.0f;
        int inferenceCount = 0;
        
    public:
        DirectMLPPOLearner(int obsSize, int numActions, const Config& config, torch::Device device);
        ~DirectMLPPOLearner();

        // Initialization
        bool Initialize();
        bool IsDirectMLEnabled() const { return usingDirectML; }
        
        // Inference operations (potentially accelerated by DirectML)
        void InferActions(
            torch::Tensor obs, 
            torch::Tensor actionMasks, 
            torch::Tensor* outActions, 
            torch::Tensor* outLogProbs
        );
        
        torch::Tensor InferCritic(torch::Tensor obs);
        
        // Training operations (uses CPU learner)
        void Learn(class ExperienceBuffer& experience, Report& report, bool isFirstIteration);
        
        // Transfer learning
        void TransferLearn(
            class ModelSet& oldModels,
            torch::Tensor newObs, torch::Tensor oldObs,
            torch::Tensor newActionMasks, torch::Tensor oldActionMasks,
            torch::Tensor actionMaps,
            Report& report,
            const class TransferLearnConfig& tlConfig
        );
        
        // Model management
        void Save(std::filesystem::path folder, bool saveOptims = true);
        void Load(std::filesystem::path folder, bool allowNotExist, bool loadOptims = true);
        
        // Performance monitoring
        void LogPerformanceMetrics(Report& report);
        std::map<std::string, std::string> GetDeviceInfo();
        
        // Access to underlying models
        class ModelSet& GetModels();
        
    private:
        // Helper functions
        bool InitializeDirectML(int obsSize, int numActions);
        bool InitializeCPULearner(int obsSize, int numActions, torch::Device device);
        
        // Performance comparison
        void BenchmarkInference(torch::Tensor obs, torch::Tensor actionMasks);
        bool ShouldUseDirectML() const;
        
        // Tensor conversion helpers
        std::vector<float> TensorToVector(const torch::Tensor& tensor);
        torch::Tensor VectorToTensor(const std::vector<float>& vec, const std::vector<int64_t>& shape);
    };

    // Factory function for easy creation
    std::unique_ptr<DirectMLPPOLearner> CreateDirectMLPPOLearner(
        int obsSize,
        int numActions,
        const PPOLearnerConfig& ppoConfig,
        torch::Device device = torch::kCPU,
        bool enableDirectML = true
    );

} // namespace GGL

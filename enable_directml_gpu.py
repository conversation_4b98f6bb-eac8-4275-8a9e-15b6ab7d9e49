"""
DirectML GPU Enabler for GigaLearnCPP
This script enables DirectML GPU acceleration by monkey-patching PyTorch operations
"""

import sys
import os
import time
import psutil
import threading
from pathlib import Path

def check_gpu_usage():
    """Monitor GPU usage to verify DirectML is working"""
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            for gpu in gpus:
                print(f"GPU {gpu.id}: {gpu.name} - Load: {gpu.load*100:.1f}% - Memory: {gpu.memoryUtil*100:.1f}%")
        else:
            print("No NVIDIA GPUs detected (expected for AMD GPU)")
    except ImportError:
        print("GPUtil not available - install with: pip install GPUtil")
    
    # Check DirectML device usage (if available)
    try:
        import torch_directml
        print("DirectML backend available")
    except ImportError:
        print("DirectML not available")

def setup_directml_environment():
    """Set up environment variables for optimal DirectML performance"""
    
    # DirectML environment variables
    os.environ['PYTORCH_DIRECTML_DEBUG'] = '1'  # Enable debug output
    os.environ['DIRECTML_FORCE_DEVICE'] = '0'   # Use first GPU
    
    print("DirectML environment configured")

def patch_torch_for_directml():
    """Monkey patch PyTorch to use DirectML by default"""
    
    try:
        import torch
        import torch_directml
        
        # Get DirectML device
        directml_device = torch_directml.device()
        print(f"DirectML device: {directml_device}")
        
        # Store original functions
        original_tensor = torch.tensor
        original_randn = torch.randn
        original_zeros = torch.zeros
        original_ones = torch.ones
        
        # Patch tensor creation functions to use DirectML by default
        def patched_tensor(*args, **kwargs):
            if 'device' not in kwargs:
                kwargs['device'] = directml_device
            return original_tensor(*args, **kwargs)
        
        def patched_randn(*args, **kwargs):
            if 'device' not in kwargs:
                kwargs['device'] = directml_device
            return original_randn(*args, **kwargs)
        
        def patched_zeros(*args, **kwargs):
            if 'device' not in kwargs:
                kwargs['device'] = directml_device
            return original_zeros(*args, **kwargs)
        
        def patched_ones(*args, **kwargs):
            if 'device' not in kwargs:
                kwargs['device'] = directml_device
            return original_ones(*args, **kwargs)
        
        # Apply patches
        torch.tensor = patched_tensor
        torch.randn = patched_randn
        torch.zeros = patched_zeros
        torch.ones = patched_ones
        
        print("✓ PyTorch patched to use DirectML by default")
        
        # Test the patch
        test_tensor = torch.randn(100, 100)
        print(f"✓ Test tensor device: {test_tensor.device}")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to patch PyTorch: {e}")
        return False

def create_directml_model_wrapper():
    """Create a wrapper that forces models to use DirectML"""
    
    try:
        import torch
        import torch.nn as nn
        import torch_directml
        
        directml_device = torch_directml.device()
        
        # Store original Module.to method
        original_to = nn.Module.to
        
        def patched_to(self, *args, **kwargs):
            # If no device specified, use DirectML
            if len(args) == 0 or (len(args) == 1 and isinstance(args[0], torch.dtype)):
                return original_to(self, directml_device)
            else:
                return original_to(self, *args, **kwargs)
        
        # Apply patch
        nn.Module.to = patched_to
        
        print("✓ Model.to() patched to use DirectML by default")
        return True
        
    except Exception as e:
        print(f"✗ Failed to patch model.to(): {e}")
        return False

def monitor_gpu_usage():
    """Monitor GPU usage in a separate thread"""
    
    def monitor_loop():
        while True:
            try:
                # Check system memory and CPU
                memory = psutil.virtual_memory()
                cpu_percent = psutil.cpu_percent(interval=1)
                
                print(f"[Monitor] CPU: {cpu_percent:.1f}% | RAM: {memory.percent:.1f}% | Available: {memory.available // (1024**3):.1f}GB")
                
                # Try to check GPU if available
                check_gpu_usage()
                
            except Exception as e:
                print(f"[Monitor] Error: {e}")
            
            time.sleep(10)  # Check every 10 seconds
    
    monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
    monitor_thread.start()
    print("✓ GPU usage monitor started")

def test_directml_performance():
    """Test DirectML performance with realistic workload"""
    
    try:
        import torch
        import torch_directml
        import time
        
        device = torch_directml.device()
        print(f"\nTesting DirectML performance on {device}")
        
        # Create a model similar to your PPO setup
        model = torch.nn.Sequential(
            torch.nn.Linear(107, 256),  # Typical RL obs size
            torch.nn.LayerNorm(256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 256),
            torch.nn.LayerNorm(256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 256),
            torch.nn.LayerNorm(256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 256),
            torch.nn.LayerNorm(256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 8)  # Typical action space
        ).to(device)
        
        # Test with different batch sizes
        batch_sizes = [1, 4, 16, 64, 256]
        
        for batch_size in batch_sizes:
            # Warm up
            for _ in range(10):
                obs = torch.randn(batch_size, 107).to(device)
                with torch.no_grad():
                    output = model(obs)
                    probs = torch.softmax(output, dim=-1)
            
            # Benchmark
            start_time = time.time()
            iterations = 100
            
            for _ in range(iterations):
                obs = torch.randn(batch_size, 107).to(device)
                with torch.no_grad():
                    output = model(obs)
                    probs = torch.softmax(output, dim=-1)
            
            end_time = time.time()
            avg_time = (end_time - start_time) / iterations * 1000
            
            print(f"Batch size {batch_size:3d}: {avg_time:.2f} ms per inference")
        
        print("✓ DirectML performance test completed")
        return True
        
    except Exception as e:
        print(f"✗ DirectML performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to enable DirectML GPU acceleration"""
    
    print("=== DirectML GPU Enabler for GigaLearnCPP ===")
    
    # Setup environment
    setup_directml_environment()
    
    # Check if DirectML is available
    try:
        import torch_directml
        print("✓ DirectML is available")
    except ImportError:
        print("✗ DirectML not installed. Run: pip install torch-directml")
        return False
    
    # Patch PyTorch for DirectML
    if not patch_torch_for_directml():
        return False
    
    # Patch model operations
    if not create_directml_model_wrapper():
        return False
    
    # Test performance
    if not test_directml_performance():
        return False
    
    # Start monitoring
    monitor_gpu_usage()
    
    print("\n🚀 DirectML GPU acceleration is now enabled!")
    print("Your GigaLearnCPP training should now use the AMD GPU for neural network operations.")
    print("Monitor the output above to verify GPU usage.")
    print("\nTo use this in your training:")
    print("1. Run this script before starting your training")
    print("2. Or import this module in your Python code")
    print("3. Check the monitor output to verify GPU usage")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\nPress Ctrl+C to stop monitoring...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
    else:
        print("\nDirectML enabler failed.")
        sys.exit(1)

"""
Performance benchmark for DirectML vs CPU inference
"""

import sys
import time
import numpy as np
import torch

# Add our scripts to path
sys.path.append('python_scripts')
import directml_service

def benchmark_cpu_inference(obs_size, num_actions, batch_size, num_iterations):
    """Benchmark CPU inference"""
    print(f"\nBenchmarking CPU inference...")
    
    # Create CPU model
    model = torch.nn.Sequential(
        torch.nn.Linear(obs_size, 256),
        torch.nn.LayerNorm(256),
        torch.nn.ReLU(),
        torch.nn.Linear(256, 256),
        torch.nn.LayerNorm(256),
        torch.nn.ReLU(),
        torch.nn.Linear(256, 256),
        torch.nn.LayerNorm(256),
        torch.nn.ReLU(),
        torch.nn.Linear(256, 256),
        torch.nn.LayerNorm(256),
        torch.nn.ReLU(),
        torch.nn.Linear(256, num_actions)
    )
    
    # Warm up
    for _ in range(10):
        obs = torch.randn(batch_size, obs_size)
        with torch.no_grad():
            output = model(obs)
            probs = torch.softmax(output, dim=-1)
    
    # Benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        obs = torch.randn(batch_size, obs_size)
        with torch.no_grad():
            output = model(obs)
            probs = torch.softmax(output, dim=-1)
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / num_iterations * 1000  # ms
    
    print(f"CPU: {avg_time:.2f} ms per inference")
    return avg_time

def benchmark_directml_inference(obs_size, num_actions, batch_size, num_iterations):
    """Benchmark DirectML inference"""
    print(f"\nBenchmarking DirectML inference...")
    
    # Initialize DirectML service
    config = {
        'obs_size': obs_size,
        'num_actions': num_actions,
        'shared_head': {'layer_sizes': [256, 256], 'activation': 'relu', 'add_layer_norm': True, 'add_output_layer': False},
        'policy': {'layer_sizes': [256, 256, 256, 256], 'activation': 'relu', 'add_layer_norm': True, 'add_output_layer': True},
        'critic': {'layer_sizes': [256, 256, 256, 256], 'activation': 'relu', 'add_layer_norm': True, 'add_output_layer': True}
    }
    
    success = directml_service.init_models(config)
    if not success:
        print("Failed to initialize DirectML models")
        return float('inf')
    
    # Warm up
    for _ in range(10):
        obs = np.random.randn(batch_size, obs_size).astype(np.float32)
        masks = np.ones((batch_size, num_actions), dtype=np.uint8)
        result = directml_service.forward_pass(obs, masks, 'policy')
    
    # Benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        obs = np.random.randn(batch_size, obs_size).astype(np.float32)
        masks = np.ones((batch_size, num_actions), dtype=np.uint8)
        result = directml_service.forward_pass(obs, masks, 'policy')
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / num_iterations * 1000  # ms
    
    print(f"DirectML: {avg_time:.2f} ms per inference")
    return avg_time

def main():
    print("=== DirectML Performance Benchmark ===")
    
    # Test parameters (matching your GigaLearnCPP setup)
    obs_size = 107  # Typical RocketLeague observation size
    num_actions = 8  # Typical discrete action space
    batch_sizes = [1, 4, 16, 64, 256]  # Different batch sizes
    num_iterations = 100
    
    print(f"Observation size: {obs_size}")
    print(f"Action space: {num_actions}")
    print(f"Iterations per test: {num_iterations}")
    
    results = []
    
    for batch_size in batch_sizes:
        print(f"\n{'='*50}")
        print(f"Batch size: {batch_size}")
        print(f"{'='*50}")
        
        # Benchmark CPU
        cpu_time = benchmark_cpu_inference(obs_size, num_actions, batch_size, num_iterations)
        
        # Benchmark DirectML
        directml_time = benchmark_directml_inference(obs_size, num_actions, batch_size, num_iterations)
        
        # Calculate speedup
        if directml_time > 0 and directml_time != float('inf'):
            speedup = cpu_time / directml_time
            print(f"\nSpeedup: {speedup:.2f}x")
            
            if speedup > 1.0:
                print(f"DirectML is {speedup:.2f}x FASTER than CPU")
            else:
                print(f"CPU is {1/speedup:.2f}x faster than DirectML")
        else:
            speedup = 0
            print("\nDirectML benchmark failed")
        
        results.append({
            'batch_size': batch_size,
            'cpu_time': cpu_time,
            'directml_time': directml_time,
            'speedup': speedup
        })
    
    # Summary
    print(f"\n{'='*60}")
    print("PERFORMANCE SUMMARY")
    print(f"{'='*60}")
    print(f"{'Batch Size':<12} {'CPU (ms)':<12} {'DirectML (ms)':<15} {'Speedup':<10}")
    print("-" * 60)
    
    for result in results:
        if result['directml_time'] != float('inf'):
            print(f"{result['batch_size']:<12} {result['cpu_time']:<12.2f} {result['directml_time']:<15.2f} {result['speedup']:<10.2f}x")
        else:
            print(f"{result['batch_size']:<12} {result['cpu_time']:<12.2f} {'FAILED':<15} {'N/A':<10}")
    
    # Recommendations
    print(f"\n{'='*60}")
    print("RECOMMENDATIONS")
    print(f"{'='*60}")
    
    best_speedup = max([r['speedup'] for r in results if r['speedup'] > 0], default=0)
    if best_speedup > 1.2:
        print(f"✓ DirectML shows significant speedup (up to {best_speedup:.2f}x)")
        print("✓ Recommended: Enable DirectML in your GigaLearnCPP configuration")
        print("✓ Expected improvement in consumption steps/second")
    elif best_speedup > 1.0:
        print(f"~ DirectML shows modest speedup ({best_speedup:.2f}x)")
        print("~ Consider enabling DirectML for marginal improvements")
    else:
        print("✗ DirectML does not show performance benefits")
        print("✗ Recommended: Keep using CPU for now")
        print("✗ This may be due to overhead or small batch sizes")

if __name__ == "__main__":
    main()

#include "GigaLearnCPP/Util/DirectMLService.h"
#include <pybind11/stl.h>
#include <chrono>
#include <iostream>

namespace py = pybind11;

namespace GGL {

DirectMLService::DirectMLService() {
    // Constructor - initialization happens in Initialize()
}

DirectMLService::~DirectMLService() {
    // Destructor - Python objects are automatically cleaned up
}

bool DirectMLService::Initialize(const ServiceConfig& cfg) {
    config = cfg;
    lastError.clear();
    
    try {
        RG_LOG("Initializing DirectML Service...");
        
        // Import the DirectML service module
        pyService = py::module::import("python_scripts.directml_service");
        
        // Check if DirectML is available
        auto deviceInfo = pyService.attr("get_device_info")().cast<std::map<std::string, py::object>>();
        
        directMLAvailable = deviceInfo["directml_available"].cast<bool>();
        bool torchAvailable = deviceInfo["torch_available"].cast<bool>();
        
        if (!torchAvailable) {
            lastError = "PyTorch not available";
            RG_ERR_CLOSE("DirectMLService: " << lastError);
            return false;
        }
        
        if (directMLAvailable) {
            RG_LOG(" > DirectML available and initialized");
        } else {
            if (config.fallbackToCPU) {
                RG_LOG(" > DirectML not available, falling back to CPU");
                fallbackMode = true;
            } else {
                lastError = "DirectML not available and fallback disabled";
                RG_ERR_CLOSE("DirectMLService: " << lastError);
                return false;
            }
        }
        
        // Initialize models
        if (!InitializeModels()) {
            return false;
        }
        
        isInitialized = true;
        RG_LOG(" > DirectML Service initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        lastError = std::string("Python initialization failed: ") + e.what();
        RG_ERR_CLOSE("DirectMLService: " << lastError);
        return false;
    }
}

bool DirectMLService::InitializeModels() {
    try {
        // Convert config to Python dictionary
        py::dict modelConfigs = ConfigToPythonDict(config);
        
        // Initialize models in Python
        bool success = pyService.attr("init_models")(modelConfigs).cast<bool>();
        
        if (!success) {
            lastError = "Model initialization failed in Python";
            return false;
        }
        
        RG_LOG(" > Models initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        lastError = std::string("Model initialization failed: ") + e.what();
        RG_ERR_CLOSE("DirectMLService: " << lastError);
        return false;
    }
}

DirectMLService::InferenceResult DirectMLService::ForwardPass(
    const std::vector<float>& observations,
    const std::vector<uint8_t>& actionMasks,
    const std::string& modelName) {
    
    InferenceResult result;
    
    if (!isInitialized) {
        lastError = "Service not initialized";
        return result;
    }
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        // Convert to numpy arrays
        py::array_t<float> obsArray = py::cast(observations);
        py::array_t<uint8_t> maskArray = py::cast(actionMasks);
        
        // Reshape arrays appropriately
        int batchSize = observations.size() / config.obsSize;
        obsArray = obsArray.reshape({batchSize, config.obsSize});
        maskArray = maskArray.reshape({batchSize, config.numActions});
        
        // Call Python forward pass
        py::dict pyResult = pyService.attr("forward_pass")(obsArray, maskArray, modelName);
        
        // Extract results
        py::array_t<float> output = pyResult["output"];
        result.output = output.cast<std::vector<float>>();
        
        if (pyResult.contains("probabilities")) {
            py::array_t<float> probs = pyResult["probabilities"];
            result.probabilities = probs.cast<std::vector<float>>();
        }
        
        if (pyResult.contains("log_probabilities")) {
            py::array_t<float> logProbs = pyResult["log_probabilities"];
            result.logProbabilities = logProbs.cast<std::vector<float>>();
        }
        
        result.success = true;
        
        // Update performance metrics
        auto endTime = std::chrono::high_resolution_clock::now();
        float inferenceTime = std::chrono::duration<float, std::milli>(endTime - startTime).count();
        
        inferenceCount++;
        avgInferenceTime = (avgInferenceTime * (inferenceCount - 1) + inferenceTime) / inferenceCount;
        
        return result;
        
    } catch (const std::exception& e) {
        lastError = std::string("Forward pass failed: ") + e.what();
        RG_ERR_CLOSE("DirectMLService: " << lastError);
        return result;
    }
}

DirectMLService::InferenceResult DirectMLService::BatchForwardPass(
    const torch::Tensor& observations,
    const torch::Tensor& actionMasks,
    const std::string& modelName) {
    
    // Convert torch tensors to vectors for now
    // TODO: Optimize this to avoid copying
    auto obsVec = TensorToVector(observations);
    auto maskVec = std::vector<uint8_t>();
    
    if (actionMasks.defined()) {
        auto maskData = actionMasks.contiguous().data_ptr<uint8_t>();
        maskVec.assign(maskData, maskData + actionMasks.numel());
    }
    
    return ForwardPass(obsVec, maskVec, modelName);
}

std::map<std::string, std::string> DirectMLService::GetDeviceInfo() {
    std::map<std::string, std::string> info;
    
    if (!isInitialized) {
        info["status"] = "not_initialized";
        return info;
    }
    
    try {
        auto pyInfo = pyService.attr("get_device_info")().cast<std::map<std::string, py::object>>();
        
        for (const auto& pair : pyInfo) {
            info[pair.first] = py::str(pair.second).cast<std::string>();
        }
        
        info["cpp_status"] = "initialized";
        info["directml_enabled"] = IsDirectMLEnabled() ? "true" : "false";
        info["fallback_mode"] = fallbackMode ? "true" : "false";
        
    } catch (const std::exception& e) {
        info["error"] = e.what();
    }
    
    return info;
}

void DirectMLService::LogPerformanceMetrics(Report& report) {
    if (inferenceCount > 0) {
        report["DirectML Avg Inference Time (ms)"] = avgInferenceTime;
        report["DirectML Inference Count"] = (float)inferenceCount;
        report["DirectML Enabled"] = IsDirectMLEnabled() ? 1.0f : 0.0f;
        report["DirectML Fallback Mode"] = fallbackMode ? 1.0f : 0.0f;
    }
}

bool DirectMLService::CheckPythonDependencies() {
    try {
        py::module::import("torch");
        py::module::import("torch_directml");
        return true;
    } catch (...) {
        return false;
    }
}

py::dict DirectMLService::ConfigToPythonDict(const ServiceConfig& cfg) {
    py::dict result;
    
    result["obs_size"] = cfg.obsSize;
    result["num_actions"] = cfg.numActions;
    
    // Shared head config
    py::dict sharedHead;
    sharedHead["layer_sizes"] = cfg.sharedHead.layerSizes;
    sharedHead["activation"] = cfg.sharedHead.activation;
    sharedHead["add_layer_norm"] = cfg.sharedHead.addLayerNorm;
    sharedHead["add_output_layer"] = cfg.sharedHead.addOutputLayer;
    result["shared_head"] = sharedHead;
    
    // Policy config
    py::dict policy;
    policy["layer_sizes"] = cfg.policy.layerSizes;
    policy["activation"] = cfg.policy.activation;
    policy["add_layer_norm"] = cfg.policy.addLayerNorm;
    policy["add_output_layer"] = cfg.policy.addOutputLayer;
    result["policy"] = policy;
    
    // Critic config
    py::dict critic;
    critic["layer_sizes"] = cfg.critic.layerSizes;
    critic["activation"] = cfg.critic.activation;
    critic["add_layer_norm"] = cfg.critic.addLayerNorm;
    critic["add_output_layer"] = cfg.critic.addOutputLayer;
    result["critic"] = critic;
    
    return result;
}

std::vector<float> DirectMLService::TensorToVector(const torch::Tensor& tensor) {
    auto flatTensor = tensor.contiguous().cpu();
    auto dataPtr = flatTensor.data_ptr<float>();
    return std::vector<float>(dataPtr, dataPtr + flatTensor.numel());
}

torch::Tensor DirectMLService::VectorToTensor(const std::vector<float>& vec, const std::vector<int64_t>& shape) {
    auto tensor = torch::from_blob(const_cast<float*>(vec.data()), shape, torch::kFloat);
    return tensor.clone();
}

// Factory function
std::unique_ptr<DirectMLService> CreateDirectMLService(
    int obsSize, 
    int numActions,
    const std::vector<int>& policyLayers,
    const std::vector<int>& criticLayers,
    const std::vector<int>& sharedLayers) {
    
    auto service = std::make_unique<DirectMLService>();
    
    DirectMLService::ServiceConfig config;
    config.obsSize = obsSize;
    config.numActions = numActions;
    config.policy.layerSizes = policyLayers;
    config.critic.layerSizes = criticLayers;
    config.sharedHead.layerSizes = sharedLayers;
    
    if (service->Initialize(config)) {
        return service;
    } else {
        return nullptr;
    }
}

} // namespace GGL

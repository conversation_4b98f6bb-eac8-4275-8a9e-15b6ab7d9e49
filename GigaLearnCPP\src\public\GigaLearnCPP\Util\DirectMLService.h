#pragma once

#include "../Framework.h"
#include "Report.h"
#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <torch/torch.h>
#include <memory>

namespace GGL {

    /**
     * DirectML Service for GPU acceleration on AMD GPUs
     * Provides seamless integration between C++ and Python DirectML backend
     */
    class RG_IMEXPORT DirectMLService {
    public:
        struct ModelConfig {
            std::vector<int> layerSizes;
            std::string activation = "relu";
            bool addLayerNorm = true;
            bool addOutputLayer = true;
        };

        struct ServiceConfig {
            int obsSize;
            int numActions;
            ModelConfig sharedHead;
            ModelConfig policy;
            ModelConfig critic;
            bool enableDirectML = true;
            bool fallbackToCPU = true;
        };

    private:
        pybind11::module pyService;
        ServiceConfig config;
        bool isInitialized = false;
        bool directMLAvailable = false;
        bool fallbackMode = false;

    public:
        DirectMLService();
        ~DirectMLService();

        // Initialization
        bool Initialize(const ServiceConfig& config);
        bool IsAvailable() const { return isInitialized; }
        bool IsDirectMLEnabled() const { return directMLAvailable && !fallbackMode; }
        
        // Model operations
        bool InitializeModels();
        
        // Inference operations
        struct InferenceResult {
            std::vector<float> output;
            std::vector<float> probabilities;
            std::vector<float> logProbabilities;
            bool success = false;
        };
        
        InferenceResult ForwardPass(
            const std::vector<float>& observations,
            const std::vector<uint8_t>& actionMasks,
            const std::string& modelName = "policy"
        );
        
        // Batch operations for better performance
        InferenceResult BatchForwardPass(
            const torch::Tensor& observations,
            const torch::Tensor& actionMasks,
            const std::string& modelName = "policy"
        );
        
        // Training operations (future extension)
        bool UpdateModels(const torch::Tensor& gradients);
        
        // Utility functions
        std::map<std::string, std::string> GetDeviceInfo();
        void LogPerformanceMetrics(Report& report);
        
        // Error handling
        std::string GetLastError() const { return lastError; }
        
    private:
        std::string lastError;
        
        // Helper functions
        bool CheckPythonDependencies();
        pybind11::dict ConfigToPythonDict(const ServiceConfig& config);
        std::vector<float> TensorToVector(const torch::Tensor& tensor);
        torch::Tensor VectorToTensor(const std::vector<float>& vec, const std::vector<int64_t>& shape);
        
        // Performance tracking
        mutable std::chrono::high_resolution_clock::time_point lastInferenceTime;
        mutable float avgInferenceTime = 0.0f;
        mutable int inferenceCount = 0;
    };

    // Factory function for easy creation
    std::unique_ptr<DirectMLService> CreateDirectMLService(
        int obsSize, 
        int numActions,
        const std::vector<int>& policyLayers = {256, 256, 256, 256},
        const std::vector<int>& criticLayers = {256, 256, 256, 256},
        const std::vector<int>& sharedLayers = {256, 256}
    );

} // namespace GGL

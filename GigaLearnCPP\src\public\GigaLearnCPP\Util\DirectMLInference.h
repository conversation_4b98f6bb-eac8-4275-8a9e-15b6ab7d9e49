#pragma once

#include "../Framework.h"
#include <vector>
#include <string>
#include <memory>

namespace GGL {

    /**
     * Simple DirectML Inference Wrapper
     * Provides GPU acceleration for model inference using DirectML
     */
    class RG_IMEXPORT DirectMLInference {
    public:
        struct InferenceResult {
            std::vector<float> logits;
            std::vector<float> probabilities;
            std::vector<float> logProbabilities;
            bool success = false;
        };

    private:
        bool initialized = false;
        bool directMLAvailable = false;
        std::string lastError;

    public:
        DirectMLInference();
        ~DirectMLInference();

        // Initialization
        bool Initialize();
        bool IsAvailable() const { return initialized && directMLAvailable; }
        
        // Model loading (from GigaLearnCPP checkpoint format)
        bool LoadPolicyModel(const std::string& modelPath);
        bool LoadCriticModel(const std::string& modelPath);
        
        // Inference operations
        InferenceResult InferPolicy(
            const std::vector<float>& observations,
            const std::vector<bool>& actionMasks,
            int batchSize,
            int obsSize,
            int numActions
        );
        
        std::vector<float> InferCritic(
            const std::vector<float>& observations,
            int batchSize,
            int obsSize
        );
        
        // Action sampling
        std::vector<int> SampleActions(const std::vector<float>& probabilities, int batchSize, int numActions);
        
        // Utility functions
        std::string GetDeviceInfo();
        float BenchmarkInference(int batchSize = 64, int numIterations = 100);
        std::string GetLastError() const { return lastError; }
        
    private:
        // Python module interface
        void* pyModule = nullptr;
        
        // Helper functions
        bool InitializePython();
        bool CallPythonFunction(const std::string& functionName, const std::vector<void*>& args, void* result);
    };

    // Global instance for easy access
    extern DirectMLInference* g_directMLInference;
    
    // Convenience functions
    bool InitializeDirectML();
    bool IsDirectMLAvailable();
    DirectMLInference* GetDirectMLInference();

} // namespace GGL
